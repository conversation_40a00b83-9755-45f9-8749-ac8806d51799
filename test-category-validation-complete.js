// Comprehensive test for category form validation
// This tests all validation rules for name, description, icon, and color

const testValidation = () => {
  console.log('🧪 Testing Category Form Validation\n');
  
  // Test cases for all fields
  const testCases = [
    // Name validation
    {
      field: 'name',
      value: '',
      expected: false,
      rule: 'required',
      description: 'Name is required'
    },
    {
      field: 'name',
      value: 'Valid Category Name',
      expected: true,
      rule: 'valid',
      description: 'Valid name'
    },
    
    // Description validation
    {
      field: 'description',
      value: '',
      expected: false,
      rule: 'required',
      description: 'Description is required'
    },
    {
      field: 'description',
      value: 'Short',
      expected: false,
      rule: 'minLength',
      description: 'Description less than 7 characters'
    },
    {
      field: 'description',
      value: '1234567',
      expected: true,
      rule: 'minLength',
      description: 'Description exactly 7 characters'
    },
    {
      field: 'description',
      value: 'This is a valid description with more than 7 characters',
      expected: true,
      rule: 'valid',
      description: 'Valid description'
    },
    
    // Icon validation
    {
      field: 'icon',
      value: '',
      expected: false,
      rule: 'required',
      description: 'Icon is required'
    },
    {
      field: 'icon',
      value: 'invalid-icon',
      expected: false,
      rule: 'format',
      description: 'Invalid icon format (missing colon)'
    },
    {
      field: 'icon',
      value: 'eva:',
      expected: false,
      rule: 'format',
      description: 'Invalid icon format (missing icon name)'
    },
    {
      field: 'icon',
      value: ':home-fill',
      expected: false,
      rule: 'format',
      description: 'Invalid icon format (missing library)'
    },
    {
      field: 'icon',
      value: 'eva:home-fill',
      expected: true,
      rule: 'valid',
      description: 'Valid icon format'
    },
    {
      field: 'icon',
      value: 'mdi:account-circle',
      expected: true,
      rule: 'valid',
      description: 'Valid icon format (different library)'
    },
    
    // Color validation
    {
      field: 'colorType',
      value: '',
      expected: false,
      rule: 'required',
      description: 'Color type is required'
    },
    {
      field: 'colorType',
      value: 'primary',
      expected: true,
      rule: 'valid',
      description: 'Valid predefined color'
    },
    {
      field: 'colorType',
      value: 'invalid-color',
      expected: false,
      rule: 'enum',
      description: 'Invalid color type'
    },
    
    // Custom color validation
    {
      field: 'customColor',
      value: '',
      colorType: 'custom',
      expected: false,
      rule: 'required',
      description: 'Custom color required when colorType is custom'
    },
    {
      field: 'customColor',
      value: 'invalid-hex',
      colorType: 'custom',
      expected: false,
      rule: 'format',
      description: 'Invalid hex color format'
    },
    {
      field: 'customColor',
      value: '#GGG',
      colorType: 'custom',
      expected: false,
      rule: 'format',
      description: 'Invalid hex characters'
    },
    {
      field: 'customColor',
      value: '#FF5733',
      colorType: 'custom',
      expected: true,
      rule: 'valid',
      description: 'Valid 6-digit hex color'
    },
    {
      field: 'customColor',
      value: '#F53',
      colorType: 'custom',
      expected: true,
      rule: 'valid',
      description: 'Valid 3-digit hex color'
    },
    {
      field: 'customColor',
      value: '#FF5733',
      colorType: 'primary',
      expected: true,
      rule: 'optional',
      description: 'Custom color optional when colorType is not custom'
    },
  ];
  
  // Validation functions
  const validateName = (value) => value && value.trim().length > 0;
  
  const validateDescription = (value) => value && value.trim().length >= 7;
  
  const validateIcon = (value) => {
    if (!value || value.trim().length === 0) return false;
    const iconPattern = /^[a-zA-Z0-9-]+:[a-zA-Z0-9-]+$/;
    return iconPattern.test(value);
  };
  
  const validateColorType = (value) => {
    const validTypes = ['primary', 'secondary', 'success', 'warning', 'info', 'error', 'custom'];
    return validTypes.includes(value);
  };
  
  const validateCustomColor = (value, colorType) => {
    if (colorType === 'custom') {
      if (!value || value.trim() === '') return false;
      const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      return hexPattern.test(value);
    }
    // If colorType is not custom, customColor can be empty or valid hex
    if (value && value.trim() !== '') {
      const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      return hexPattern.test(value);
    }
    return true;
  };
  
  // Run tests
  console.log('📋 Running validation tests...\n');
  
  let passCount = 0;
  let failCount = 0;
  
  testCases.forEach(({ field, value, colorType, expected, rule, description }) => {
    let result = false;
    
    switch (field) {
      case 'name':
        result = validateName(value);
        break;
      case 'description':
        result = validateDescription(value);
        break;
      case 'icon':
        result = validateIcon(value);
        break;
      case 'colorType':
        result = validateColorType(value);
        break;
      case 'customColor':
        result = validateCustomColor(value, colorType);
        break;
    }
    
    const status = result === expected ? '✅ PASS' : '❌ FAIL';
    const valueDisplay = value === '' ? '(empty)' : `"${value}"`;
    const extraInfo = colorType ? ` [colorType: ${colorType}]` : '';
    
    console.log(`${status} - ${field}: ${valueDisplay}${extraInfo} - ${description}`);
    
    if (result === expected) {
      passCount++;
    } else {
      failCount++;
    }
  });
  
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log(`📈 Success Rate: ${((passCount / (passCount + failCount)) * 100).toFixed(1)}%`);
  
  if (failCount === 0) {
    console.log('\n🎉 All validation tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the validation logic.');
  }
  
  console.log('\n📝 Validation Summary:');
  console.log('• Name: Required, non-empty string');
  console.log('• Description: Required, minimum 7 characters');
  console.log('• Icon: Required, format "library:icon-name"');
  console.log('• ColorType: Required, must be valid enum value');
  console.log('• CustomColor: Required when colorType is "custom", must be valid hex');
};

testValidation();
