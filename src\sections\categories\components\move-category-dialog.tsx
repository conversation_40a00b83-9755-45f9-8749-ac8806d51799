import { useState, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Stack,
  IconButton,
  TextField,
  MenuItem,
  ListItemIcon,
  ListItemText,
  InputAdornment,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Category } from 'src/services/api/use-categories-api';

interface MoveCategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (newCategoryId: number) => void;
  sourceCategory: Category | null;
  availableCategories: Category[];
  isMoving?: boolean;
}

export default function MoveCategoryDialog({
  open,
  onClose,
  onConfirm,
  sourceCategory,
  availableCategories,
  isMoving = false,
}: MoveCategoryDialogProps) {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter out the source category from available options
  const filteredCategories = availableCategories.filter(
    (category) => sourceCategory && category.id !== sourceCategory.id
  );

  // Filter categories based on search query
  const searchFilteredCategories = useMemo(() => {
    if (!searchQuery.trim()) {
      return filteredCategories;
    }
    return filteredCategories.filter((category) =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [filteredCategories, searchQuery]);

  const handleConfirm = () => {
    if (selectedCategoryId) {
      onConfirm(selectedCategoryId);
    }
  };

  const handleClose = () => {
    setSelectedCategoryId(null);
    setSearchQuery('');
    onClose();
  };

  // Don't render if no source category
  if (!sourceCategory) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxWidth: 500,
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
 <Typography variant="h6" sx={{ mb: 1 }}>
              Move Agents to Another Category
            </Typography>          <IconButton onClick={handleClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={0}>
          <Box>
           
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This category contains {sourceCategory.templatesCount || 0} agents.
            </Typography>
          </Box>

          <Box>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Move {sourceCategory.templatesCount || 0} agents to
            </Typography>

            {filteredCategories.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  No other categories available to move content to.
                </Typography>
              </Box>
            ) : (
              <TextField
                select
                fullWidth
                value={selectedCategoryId || ''}
                onChange={(e) => setSelectedCategoryId(Number(e.target.value) || null)}
                placeholder="Select Destination Category"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="eva:search-fill" width={20} height={20} />
                    </InputAdornment>
                  ),
                }}
                SelectProps={{
                  displayEmpty: true,
                  renderValue: (value) => {
                    if (!value) {
                      return (
                        <Typography variant="body2" color="text.secondary">
                          Select Destination Category
                        </Typography>
                      );
                    }
                    const category = searchFilteredCategories.find(cat => cat.id === value);
                    if (!category) return '';
                    return (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: category.theme || 'primary.main',
                            color: 'white',
                          }}
                        >
                          <Iconify icon={category.icon || 'eva:folder-fill'} width={12} height={12} />
                        </Box>
                        <Typography variant="body2">{category.name}</Typography>
                      </Box>
                    );
                  },
                  MenuProps: {
                    PaperProps: {
                      sx: {
                        maxHeight: 300,
                        '& .MuiMenuItem-root': {
                          px: 2,
                          py: 1,
                        },
                      },
                    },
                  },
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  },
                }}
              >
                {/* Search field as first menu item */}
                <MenuItem disabled sx={{ px: 2, py: 1 }}>
                  <TextField
                    size="small"
                    fullWidth
                    placeholder="Search categories..."
                    value={searchQuery}
                    onChange={(e) => {
                      e.stopPropagation();
                      setSearchQuery(e.target.value);
                    }}
                    onClick={(e) => e.stopPropagation()}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Iconify icon="eva:search-fill" width={16} height={16} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 1,
                      },
                    }}
                  />
                </MenuItem>

                {searchFilteredCategories.length === 0 && searchQuery ? (
                  <MenuItem disabled>
                    <Typography variant="body2" color="text.secondary">
                      No categories found matching &ldquo;{searchQuery}&rdquo;
                    </Typography>
                  </MenuItem>
                ) : (
                  searchFilteredCategories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 32,
                            height: 32,
                            borderRadius: '50%',
                            bgcolor: category.theme || 'primary.main',
                            color: 'white',
                          }}
                        >
                          <Iconify icon={category.icon || 'eva:folder-fill'} width={16} height={16} />
                        </Box>
                      </ListItemIcon>
                      <ListItemText
                        primary={category.name}
                        secondary={`${category.templatesCount || 0} agents`}
                        primaryTypographyProps={{
                          variant: 'subtitle2',
                        }}
                        secondaryTypographyProps={{
                          variant: 'caption',
                          color: 'text.secondary',
                        }}
                      />
                    </MenuItem>
                  ))
                )}
              </TextField>
            )}
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1,backgroundColor:'divider' }}>
        <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={handleClose}
            sx={{
              height: '32px',
              width:"25%",
              px: 1,
              borderRadius: 1,
              textTransform: 'none',
            }}
          />
          <AppButton
            variant="contained"
            color="error"
            label="Move & Delete"
            onClick={handleConfirm}
            disabled={!selectedCategoryId || filteredCategories.length === 0 }
            isLoading={isMoving}
            sx={{
              whiteSpace:'nowrap',
              height: '32px',
              width:"25%",
              px: 1,
              borderRadius: 1,
              textTransform: 'none',
            }}
          />
        </Stack>
      </DialogActions>
    </Dialog>
  );
}
