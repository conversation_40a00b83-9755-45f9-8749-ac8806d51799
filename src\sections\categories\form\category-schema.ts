import * as z from 'zod';

// Create schema with translation function
export const createCategorySchema = (t: any) => z.object({
  name: z.string().min(1, t('categories.validation.nameRequired')),
  description: z.string()
    .min(1, t('categories.validation.descriptionRequired'))
    .min(7, t('categories.validation.descriptionMinLength')),
  icon: z.string().min(1, t('categories.validation.iconRequired')),
  colorType: z.enum(['primary', 'secondary', 'success', 'warning', 'info', 'error', 'custom']),
  customColor: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, t('categories.validation.invalidHexColor'))
    .optional(),
});

// Default schema for backward compatibility (without translations)
export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string()
    .min(1, 'Description is required')
    .min(7, 'Description must be at least 7 characters'),
  icon: z.string().min(1, 'Icon is required'),
  colorType: z.enum(['primary', 'secondary', 'success', 'warning', 'info', 'error', 'custom']),
  customColor: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Invalid hex color')
    .optional(),
});

export type CategoryFormValues = z.infer<typeof categorySchema>;
