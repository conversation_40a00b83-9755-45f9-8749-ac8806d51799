import {
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormHelperText,
  Stack,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  IconButton,
  Popover,
} from '@mui/material';
import { useState } from 'react';
import { Iconify } from 'src/components/iconify';
import { Controller, Control } from 'react-hook-form';
import { CategoryFormValues } from './category-schema';

interface ColorOption {
  value: string;
  label: string;
  color: string;
  icon?: string;
}

interface ColorSelectorProps {
  control: Control<CategoryFormValues>;
  error?: boolean;
  helperText?: string;
}

export default function ColorSelector({ control, error, helperText }: ColorSelectorProps) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const colorOptions: ColorOption[] = [
    { value: 'primary', label: 'Primary', color: theme.palette.primary.main },
    { value: 'secondary', label: 'Secondary', color: theme.palette.secondary.main },
    { value: 'success', label: 'Success', color: theme.palette.success.main },
    { value: 'warning', label: 'Warning', color: theme.palette.warning.main },
    { value: 'info', label: 'Info', color: theme.palette.info.main },
    { value: 'error', label: 'Error', color: theme.palette.error.main },
    { value: 'custom', label: 'Custom', color: '#FF5733', icon: 'eva:plus-fill' },
  ];

  const handleOpenPopover = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Controller
        name="colorType"
        control={control}
        render={({ field }) => {
          const selectedOption = colorOptions.find(option => option.value === field.value) || colorOptions[0];

          return (
            <Stack spacing={1}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                Choose Color
              </Typography>
              <Box
                component="button"
                type="button"
                onClick={handleOpenPopover}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                  p: 2,
                  border: (theme) => `1px solid ${theme.palette.divider}`,
                  borderRadius: 2,
                  bgcolor: 'background.paper',
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'action.hover',
                  },
                  '&:focus': {
                    outline: 'none',
                    borderColor: 'primary.main',
                  },
                }}
              >
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      bgcolor: selectedOption.color,
                    }}
                  />
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Color-name
                  </Typography>
                </Stack>
                <Iconify icon="eva:chevron-down-fill" width={16} height={16} />
              </Box>

              <Popover
                open={open}
                anchorEl={anchorEl}
                onClose={handleClosePopover}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                slotProps={{
                  paper: {
                    sx: {
                      p: 2,
                      width: 300,
                      maxHeight: 200,
                    },
                  },
                }}
              >
                <Stack spacing={2}>
                  <Typography variant="subtitle1">
                    Select Color
                  </Typography>

                  <FormControl component="fieldset" error={error}>
                     {/* Custom Color Input */}
                  <Controller
                    name="customColor"
                    control={control}
                    render={({ field: customColorField, fieldState: { error: customColorError } }) => (
                      <Box sx={{ display: field.value === 'custom' ? 'block' : 'none' }}>
                        <TextField
                          {...customColorField}
                          fullWidth
                          label="Custom Color (Hex)"
                          placeholder="#FF5733"
                          error={!!customColorError}
                          helperText={customColorError?.message}
                          onKeyDown={(e) => {
                            // Prevent form submission on Enter key
                            if (e.key === 'Enter') {
                              e.preventDefault();
                            }
                          }}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Box
                                  sx={{
                                    width: 20,
                                    height: 20,
                                    borderRadius: '50%',
                                    bgcolor: customColorField.value || '#FF5733',
                                    mr: 1,
                                  }}
                                />
                              </InputAdornment>
                            ),
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton type="button" sx={{ p: 0.5 }} edge="end" onClick={(e) => e.stopPropagation()}>
                                  <Box
                                    component="input"
                                    type="color"
                                    value={customColorField.value || '#FF5733'}
                                    onChange={(e) => customColorField.onChange(e.target.value)}
                                    sx={{
                                      width: '100%',
                                      height: '100%',
                                      border: 'none',
                                      padding: 0,
                                      cursor: 'pointer',
                                      opacity: 0,
                                      position: 'absolute',
                                    }}
                                  />
                                  <Iconify icon="eva:color-palette-fill" width={24} height={24} />
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Box>
                    )}
                  />
                    <RadioGroup
                      {...field}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleClosePopover();
                      }}
                    >
                      {colorOptions.map((option) => (
                        <FormControlLabel
                          key={option.value}
                          value={option.value}
                          control={
                            <Radio
                              sx={{
                                '&.Mui-checked': {
                                  color: option.color,
                                },
                              }}
                              icon={
                                option.icon ? (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: 20,
                                      height: 20,
                                      bgcolor: 'rgba(255, 226, 216, 1)',
                                      color: 'black',
                                      borderRadius: '50%',
                                    }}
                                  >
                                    <Iconify icon={option.icon} width={20} height={20} />
                                  </Box>
                                ) : (
                                  <Box
                                    sx={{
                                      width: 20,
                                      height: 20,
                                      borderRadius: '50%',
                                      bgcolor: option.color,
                                      opacity: 0.6,
                                    }}
                                  />
                                )
                              }
                              checkedIcon={
                                option.icon ? (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: 20,
                                      height: 20,
                                      bgcolor: 'rgba(255, 226, 216, 1)',
                                      color: 'black',
                                      borderRadius: '50%',
                                      boxShadow: `0 0 0 2px ${theme.palette.background.paper}, 0 0 0 4px ${option.color}`,
                                    }}
                                  >
                                    <Iconify icon={option.icon} width={20} height={20} />
                                  </Box>
                                ) : (
                                  <Box
                                    sx={{
                                      width: 20,
                                      height: 20,
                                      borderRadius: '50%',
                                      bgcolor: option.color,
                                      boxShadow: `0 0 0 2px ${theme.palette.background.paper}, 0 0 0 4px ${option.color}`,
                                    }}
                                  />
                                )
                              }
                            />
                          }
                          label={
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {option.label}
                            </Typography>
                          }
                        />
                      ))}
                    </RadioGroup>
                    {helperText && <FormHelperText>{helperText}</FormHelperText>}
                  </FormControl>

                 
                </Stack>
              </Popover>
            </Stack>
          );
        }}
      />
    </>
  );
}
