import { useState, useCallback, useEffect } from 'react';
import {
  useCategoriesApi,
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from 'src/services/api/use-categories-api';
import { useSnackbar } from 'src/components/snackbar';

import { CategoryFormValues } from '../form/category-schema';

// Helper functions to convert between form data and API data


// Helper function to convert color type to backend-compatible theme value (min 7 chars)
const convertColorTypeToTheme = (colorType: string, customColor?: string): string => {
  if (colorType === 'custom') {
    return customColor || '#FF5733';
  }

  // Map color type names to backend-compatible codes (minimum 7 characters)
  const colorTypeMap: Record<string, string> = {
    'primary': 'primary',      // 7 chars - OK
    'secondary': 'secondary',  // 9 chars - OK (keep full name)
    'success': 'success',      // 7 chars - OK
    'warning': 'warning',      // 7 chars - OK
    'info': 'info-cl',         // 7 chars - extended from 'info' (4 chars)
    'error': 'error-c',        // 7 chars - extended from 'error' (5 chars)
  };

  return colorTypeMap[colorType] || colorType;
};

const convertFormToApiRequest = (
  formData: CategoryFormValues
): CreateCategoryRequest | UpdateCategoryRequest => {
  return {
    name: formData.name,
    description: formData.description,
    icon: formData.icon,
    theme: convertColorTypeToTheme(formData.colorType, formData.customColor),
  };
};

// Helper function to convert backend theme value back to color type
const convertThemeToColorType = (theme: string): 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error' | 'custom' => {
  if (theme?.startsWith('#')) {
    return 'custom';
  }

  // Map backend codes back to color type names
  const themeToColorTypeMap: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error'> = {
    'primary': 'primary',
    'secondary': 'secondary',
    'success': 'success',
    'warning': 'warning',
    'info-cl': 'info',       // Map 'info-cl' back to 'info'
    'error-c': 'error',      // Map 'error-c' back to 'error'
  };

  return themeToColorTypeMap[theme] || 'primary';
};

const convertApiToFormData = (category: Category): CategoryFormValues => {
  // Check if theme is a hex color (custom) or a predefined color type
  const isCustomColor = category.theme.startsWith('#');

  return {
    name: category.name,
    description: category.description,
    icon: category.icon,
    colorType: convertThemeToColorType(category.theme),
    customColor: isCustomColor ? category.theme : undefined,
  };
};

const convertApiCategoryToDisplayCategory = (apiCategory: Category): Category => {
  // Convert API category to display category with additional UI properties
  // Handle cases where theme might be undefined or null
  const theme = apiCategory.theme || '#7D40D9'; // Default to primary color if theme is missing
  const isCustomColor = theme.startsWith('#');

  return {
    ...apiCategory,
    theme,
    colorType: isCustomColor ? 'custom' : (theme as any),
    customColor: isCustomColor ? theme : undefined,
    templatesCount: apiCategory.templatesCount || 0,
  };
};

// This is a custom hook that combines the API services with local state management
export const useCategoriesView = () => {
  // State for categories
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openMoveDialog, setOpenMoveDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const { enqueueSnackbar } = useSnackbar();

  // Loading states for CRUD operations
  // const [isCreating, setIsCreating] = useState(false);
  // const [isUpdating, setIsUpdating] = useState(false);
  // const [isDeleting, setIsDeleting] = useState(false);

  // Get the API hooks
  const {
    useGetCategories,
    useCreateCategory,
    useUpdateCategory,
    useDeleteCategory,
    useMoveContent,
  } = useCategoriesApi();

  // API hooks with proper loading states and refetch
  const { mutate: createCategory, isPending: isCreating } = useCreateCategory();

  const { mutate: updateCategory, isPending: isUpdating } = useUpdateCategory(
    selectedCategory?.id || 0
  );

  const { mutate: deleteCategory, isPending: isDeleting } = useDeleteCategory();

  // State for move operation
  const [moveSourceId, setMoveSourceId] = useState<number | null>(null);
  const { mutate: moveContent, isPending: isMoving } = useMoveContent(moveSourceId || 0);

  // Get categories data from the API
  const { data: categoriesResponse, isLoading, isError, refetch } = useGetCategories();

  // Update local state when API data changes
  useEffect(() => {
    if (categoriesResponse?.categories) {
      // Convert API categories to display categories
      const displayCategories = categoriesResponse.categories.map(
        convertApiCategoryToDisplayCategory
      );
      console.log('displayCategories ', displayCategories);
      setCategories(displayCategories);
      setLoading(false);
      setError(null);
    }
  }, [categoriesResponse]);

  // Update loading state
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  // Update error state
  useEffect(() => {
    if (isError) {
      setError('Failed to fetch categories');
    }
  }, [isError]);

  // Fetch categories function for manual refetching
  const fetchCategories = useCallback(() => {
    setLoading(true);
    refetch();
  }, [refetch]);

  // Initial fetch
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Dialog handlers
  const handleOpenDialog = useCallback((category?: Category) => {
    setSelectedCategory(category || null);
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedCategory(null);
  }, []);

  const handleCloseDeleteDialog = useCallback(() => {
    setOpenDeleteDialog(false);
    setSelectedCategory(null);
  }, []);

  const handleCloseMoveDialog = useCallback(() => {
    setOpenMoveDialog(false);
    setSelectedCategory(null);
  }, []);

  // Open the dialog to create a new category
  const handleOpenCreateDialog = useCallback(() => {
    setSelectedCategory(null);
    setOpenDialog(true);
  }, []);

  // Handle creating a new category
  const handleCreateCategory = useCallback(
    (data: CategoryFormValues) => {
      // Set loading state

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data);

      // Use the mutation from the API hook
      createCategory(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend and close dialog
          refetch();
          handleCloseDialog();
        },
        onError: (err) => {
          console.error('Failed to create category:', err);
        },
      });
    },
    [categories, createCategory, handleCloseDialog]
  );

  // Handle updating a category (for dialog-based editing)
  const handleUpdateCategoryDialog = useCallback(
    (data: CategoryFormValues) => {
      if (!selectedCategory) return;

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data);

      // Use the mutation from the API hook
      updateCategory(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend and close dialog
          refetch();
          handleCloseDialog();
        },
        onError: (err) => {
          console.error('Failed to update category:', err);
        },
      });
    },
    [selectedCategory, updateCategory, handleCloseDialog, refetch]
  );

  // Handle updating a category (for unified form)
  const handleUpdateCategory = useCallback(
    (data: CategoryFormValues, categoryId?: number) => {
      // For unified form, we need the category ID passed as parameter
      // For dialog form, we use selectedCategory
      const targetCategoryId = categoryId || selectedCategory?.id;

      if (!targetCategoryId) {
        console.error('No category ID provided for update');
        return;
      }

      // Convert form data to API request format
      const apiRequest = convertFormToApiRequest(data);

      // Use the mutation from the API hook
      updateCategory(apiRequest, {
        onSuccess: () => {
          // On success: refetch data from backend
          refetch();
          // Only close dialog if we're in dialog mode
          if (selectedCategory) {
            handleCloseDialog();
          }
        },
        onError: (err) => {
          console.error('Failed to update category:', err);
        },
      });
    },
    [selectedCategory, updateCategory, handleCloseDialog, refetch]
  );

  // Handle deleting a category with confirmation
  const handleDeleteCategory = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
      const category = categories.find((cat) => cat.id === numericId);

      if (category) {
        // Set the category to be deleted for confirmation dialog
        setSelectedCategory(category);
        setOpenDeleteDialog(true);
      }
    },
    [categories]
  );

  // Confirm delete category
  const handleConfirmDelete = useCallback(() => {
    if (!selectedCategory) return;

    deleteCategory(selectedCategory.id, {
      onSuccess: () => {
        refetch();
        setOpenDeleteDialog(false);
        setSelectedCategory(null);
      },
      onError: (err) => {
        console.error('Failed to delete category:', err);
      },
    });
  }, [selectedCategory, deleteCategory, refetch]);

  // Handle moving category content
  const handleMoveContent = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
      const category = categories.find((cat) => cat.id === numericId);

      if (category) {
        setSelectedCategory(category);
        setOpenMoveDialog(true);
      }
    },
    [categories]
  );

  // Confirm move content
  const handleConfirmMove = useCallback(
    (destinationCategoryId: number) => {
      if (!selectedCategory) return;

      // Set the source ID for the move operation
      setMoveSourceId(selectedCategory.id);

      // Backend API endpoint: PATCH /api/categories/{id}/move-content
      // Request body: { destinationCategoryId: number }

      moveContent(
        { destinationCategoryId },
        {
          onSuccess: () => {
            // Show success message
            enqueueSnackbar(
              `Content successfully moved from "${selectedCategory?.name}" to the selected category.`,
              { variant: 'success' }
            );

            refetch();
            setOpenMoveDialog(false);
            setSelectedCategory(null);
            setMoveSourceId(null);
          },
          onError: (err) => {
            console.error('Failed to move category content:', err);
            console.error('Backend API endpoint not implemented yet.');
            console.error('Please implement: PATCH /api/categories/{id}/move-content');
            console.error('Request body should be: { destinationCategoryId: number }');

            // Show user-friendly error message
            enqueueSnackbar(
              'Move content feature is not yet implemented. Please contact your administrator.',
              { variant: 'error' }
            );

            setMoveSourceId(null);
            setOpenMoveDialog(false);
            setSelectedCategory(null);
          },
        }
      );
    },
    [selectedCategory, refetch, moveContent]
  );

  // Handle editing a category
  const handleEditCategory = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
      const category = categories.find((cat) => cat.id === numericId);
      if (category) {
        // Convert API category to form data for editing
        const formData = convertApiToFormData(category);
        handleOpenDialog({ ...category, ...formData } as Category);
      }
    },
    [categories, handleOpenDialog]
  );

  return {
    // State
    categories,
    loading,
    error,
    openDialog,
    openDeleteDialog,
    openMoveDialog,
    selectedCategory,

    // Loading states for CRUD operations
    isLoading: isCreating || isUpdating,
    isDeleting,
    isMoving,

    // Handlers
    handleOpenDialog,
    handleCloseDialog,
    handleCloseDeleteDialog,
    handleCloseMoveDialog,
    handleCreateCategory,
    handleUpdateCategory,
    handleDeleteCategory,
    handleConfirmDelete,
    handleMoveContent,
    handleConfirmMove,
    handleEditCategory,
    handleOpenCreateDialog,

    // Refetch
    refetch: fetchCategories,
  };
};
